package com.wonderslate.data

import grails.transaction.Transactional

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import software.amazon.awssdk.services.cloudfront.CloudFrontUtilities;
import software.amazon.awssdk.services.cloudfront.model.CannedSignerRequest;
import software.amazon.awssdk.services.cloudfront.url.SignedUrl;
import java.security.MessageDigest;


@Transactional
class ContentDeliveryService {
    String keyPairId = "K1K3820LSJ1OXC" // CloudFront key pair ID
    String privateKeyPath = "supload/pk-APKA2OPFHBCSMBP7Q7RL.pem" // Path to your private key file

   def generateSignedURL(String resourceUrl) {
        try {
            CloudFrontUtilities cloudFrontUtilities = CloudFrontUtilities.create();
            Instant expirationDate = Instant.now().plus(2, ChronoUnit.MINUTES);
            CannedSignerRequest cannedRequest = CannedSignerRequest.builder()
                    .resourceUrl(resourceUrl)
                    .privateKey(new java.io.File(privateKeyPath).toPath())
                    .keyPairId(keyPairId)
                    .expirationDate(expirationDate)
                    .build();
            SignedUrl signedUrl = cloudFrontUtilities.getSignedUrlWithCannedPolicy(cannedRequest);
            System.out.println(signedUrl.url());

            // Generate token using SHA256 hash of file path and expiration time
            String tokenInput = resourceUrl + expirationDate.toEpochMilli()
            String token = generateSHA256Hash(tokenInput)

            // Store token in KeyValueMst
            KeyValueMst keyValueMst = new KeyValueMst(
                keyName: token,
                keyValue: "false"
            )
            keyValueMst.save(failOnError: true, flush: true)

            // Append token to the signed URL
            String finalUrl = signedUrl.url() + "&token=" + token

            return finalUrl
        } catch (Exception e) {
            log.error("Error generating CloudFront signed cookies: ${e.message}", e)
            throw e
        }
    }

    private String generateSHA256Hash(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256")
            byte[] hash = digest.digest(input.getBytes("UTF-8"))
            StringBuilder hexString = new StringBuilder()

            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b)
                if (hex.length() == 1) {
                    hexString.append('0')
                }
                hexString.append(hex)
            }

            return hexString.toString()
        } catch (Exception e) {
            log.error("Error generating SHA256 hash: ${e.message}", e)
            throw e
        }
    }
}
